<?php

declare(strict_types= 1);

$config = require __DIR__ . '/config.php';

$dsn = "mysql:host={$config->db->host};dbname={$config->db->dbname};charset={$config->db->charset}";
$pdo = new PDO($dsn,$config->db->user, $config->db->password, [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
]);

function e($string){
    return htmlspecialchars((string)$string, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
}

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$stmt = $pdo->prepare('SELECT * FROM articles WHERE id = :id');
$stmt->execute([':id'=> $id]);
$article = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$article){
    http_response_code(404);
    exit('Article Not Found!!');
}

?>

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?= e($article['title']) ?></title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </head>
    <body>
        <div class="container">
            <div class="row my-5">
                <div class="d-flex justify-content-end">
                    <a href="index.php" class="btn btn-dark"><i class="bi bi-arrow-left me-2"></i> Back</a>
                </div>
                <div class="col-md-12">
                    <h1 class="my-5 fw-bold"><?= e($article['title']) ?></h1>
                    <p class="text-muted">Published Date: <?= e(date('F j, Y', strtotime($article['published_date'])) ) ?></p>
                    <?php if($article['image_url']): ?>
                        <img class="mb-4 w-100 img-fluid" src="<?= e($article['image_url']) ?>" class="img-fluid" alt="<?= e($article['title']) ?>" loading="lazy">
                    <?php endif; ?>
                    <p class="lead"><?= nl2br(e($article['description'])) ?></p>
                    <a href="<?= e($article['url']) ?>" target="_blank" class="btn btn-primary">Read More</a>
                </div>
            </div>
        </div>
    </body>
</html>
