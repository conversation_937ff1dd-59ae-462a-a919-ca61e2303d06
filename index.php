<?php

declare(strict_types= 1);

$config = require __DIR__ . '/config.php';

$dsn = "mysql:host={$config->db->host};dbname={$config->db->dbname};charset={$config->db->charset}";
$pdo = new PDO($dsn,$config->db->user, $config->db->password, [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
]);

// Basic Helper Functions

function e($string){
    return htmlspecialchars((string)$string, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
}

$search = isset($_GET['search']) ? trim((string)$_GET['search']) : '';
$category = isset($_GET['category']) ? trim((string)$_GET['category']) : '';
$page = max(1, (int)($_GET['page']) ?? 1);
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Build Where Clause Safely
$where = "1=1";
$params = [];

if($search !== '') {
    $where .= ' AND (title LIKE :search OR description LIKE :search)';
    $params[':search'] = "%{$search}%";;
}

if($category !== "") {
    $where .= " AND category = :category";
    $params[":category"] = $category;
}

// Total Count
$totalstmt = $pdo->prepare("SELECT COUNT(*) FROM articles WHERE {$where}");
$totalstmt->execute($params);
$total = (int)$totalstmt->fetchColumn();
$pages = (int)ceil($total / $per_page);

// Fetch Categories For Filtering Dropdown
$categoryStmt = $pdo->query("SELECT DISTINCT category FROM articles WHERE category IS NOT NULL ORDER BY category");
$categories = $categoryStmt->fetchAll(PDO::FETCH_COLUMN);
// Fetch Articles
$sql = "SELECT * FROM articles WHERE {$where} ORDER BY published_date DESC LIMIT :limit OFFSET :offset";
$stmt = $pdo->prepare($sql);

foreach($params as $key => $value){
    $stmt->bindValue($key, $value);
}

$stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();

$articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NewsHub - Your Daily News Aggregator</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <style>
            :root {
                --primary-color: #2563eb;
                --primary-dark: #1d4ed8;
                --secondary-color: #64748b;
                --accent-color: #f59e0b;
                --success-color: #10b981;
                --background-light: #f8fafc;
                --background-white: #ffffff;
                --text-primary: #1e293b;
                --text-secondary: #64748b;
                --border-color: #e2e8f0;
                --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
                --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            }

            * {
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: var(--text-primary);
                line-height: 1.6;
            }

            .main-container {
                background: var(--background-white);
                border-radius: 20px;
                box-shadow: var(--shadow-lg);
                margin: 2rem auto;
                max-width: 1200px;
                overflow: hidden;
            }

            .header-section {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
                color: white;
                padding: 3rem 2rem 2rem;
                text-align: center;
                position: relative;
                overflow: hidden;
            }

            .header-section::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                pointer-events: none;
            }

            .header-content {
                position: relative;
                z-index: 1;
            }

            .main-title {
                font-size: 3rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .main-subtitle {
                font-size: 1.1rem;
                opacity: 0.9;
                font-weight: 300;
            }

            .search-section {
                padding: 2rem;
                background: var(--background-light);
                border-bottom: 1px solid var(--border-color);
            }

            .search-form {
                max-width: 800px;
                margin: 0 auto;
            }

            .form-control, .form-select {
                border: 2px solid var(--border-color);
                border-radius: 12px;
                padding: 0.75rem 1rem;
                font-size: 0.95rem;
                transition: all 0.3s ease;
                background: var(--background-white);
            }

            .form-control:focus, .form-select:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                outline: none;
            }

            .btn-search {
                background: var(--primary-color);
                border: none;
                border-radius: 12px;
                padding: 0.75rem 2rem;
                font-weight: 500;
                transition: all 0.3s ease;
                box-shadow: var(--shadow-sm);
            }

            .btn-search:hover {
                background: var(--primary-dark);
                transform: translateY(-1px);
                box-shadow: var(--shadow-md);
            }

            .content-section {
                padding: 2rem;
            }
        </style>
    </head>
    <body>
        <div class="main-container">
            <div class="header-section">
                <div class="header-content">
                    <h1 class="main-title">
                        <i class="bi bi-newspaper me-3"></i>NewsHub
                    </h1>
                    <p class="main-subtitle">Stay informed with the latest news from around the world</p>
                </div>
            </div>

            <div class="search-section">
                <form action="index.php" method="GET" class="search-form">
                    <div class="row g-3">
                        <div class="col-md-5">
                            <div class="input-group">
                                <span class="input-group-text bg-white border-end-0" style="border: 2px solid var(--border-color); border-right: none; border-radius: 12px 0 0 12px;">
                                    <i class="bi bi-search text-muted"></i>
                                </span>
                                <input type="text" name="search" class="form-control border-start-0 ps-0"
                                       placeholder="Search articles..." value="<?= e($search) ?>"
                                       style="border-radius: 0 12px 12px 0;">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select name="category" class="form-select">
                                <option value="">All Categories</option>
                                <?php foreach($categories as $cat): ?>
                                    <option value="<?= e($cat) ?>" <?= $cat === $category ? 'selected' : '' ?>>
                                        <?= e(ucfirst($cat)) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-search w-100">
                                <i class="bi bi-funnel me-2"></i>Filter News
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="content-section">

                    <?php if(empty($articles)): ?>
                        <p class="text-center text-muted">-- No Articles Found --</p>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach($articles as $article): ?>
                                <div class="col-md-4">
                                    <div class="card mb-4">
                                        <?php if($article['image_url']): ?>
                                            <img src="<?= e($article['image_url']) ?>" class="card-img-top" alt="<?= e($article['title']) ?>" loading="lazy">
                                        <?php endif; ?>
                                        <div class="card-body">
                                            <h5 class="card-title"><a href="view.php?id=<?= e((int)$article['id']) ?>" target="_blank"><?= e($article['title']) ?></a></h5>
                                            <p class="card-text"><small class="text-muted">Published Date: <?= e(date('F j, Y', strtotime($article['published_date'])) ) ?></small></p>
                                            <p class="card-text"><?= e(substr($article['description'] ?? '', 0, 100)) ?>...</p>
                                            <a href="<?= e($article['url']) ?>" target="_blank" class="btn btn-primary">Read More</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <?php for($i = 1; $i <= $pages; $i++): ?>
                                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&page=<?= $i ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </body>
</html>

