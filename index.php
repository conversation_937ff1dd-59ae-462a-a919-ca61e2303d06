<?php

declare(strict_types= 1);

$config = require __DIR__ . '/config.php';

$dsn = "mysql:host={$config->db->host};dbname={$config->db->dbname};charset={$config->db->charset}";
$pdo = new PDO($dsn,$config->db->user, $config->db->password, [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
]);

// Basic Helper Functions

function e($string){
    return htmlspecialchars((string)$string, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
}

$search = isset($_GET['search']) ? trim((string)$_GET['search']) : '';
$category = isset($_GET['category']) ? trim((string)$_GET['category']) : '';
$page = max(1, (int)($_GET['page']) ?? 1);
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Build Where Clause Safely
$where = "1=1";
$params = [];

if($search !== '') {
    $where .= ' AND (title LIKE :search OR description LIKE :search)';
    $params[':search'] = "%{$search}%";;
}

if($category !== "") {
    $where .= " AND category = :category";
    $params[":category"] = $category;
}

// Total Count
$totalstmt = $pdo->prepare("SELECT COUNT(*) FROM articles WHERE {$where}");
$totalstmt->execute($params);
$total = (int)$totalstmt->fetchColumn();
$pages = (int)ceil($total / $per_page);

// Fetch Categories For Filtering Dropdown
$categoryStmt = $pdo->query("SELECT DISTINCT category FROM articles WHERE category IS NOT NULL ORDER BY category");
$categories = $categoryStmt->fetchAll(PDO::FETCH_COLUMN);
// Fetch Articles
$sql = "SELECT * FROM articles WHERE {$where} ORDER BY published_date DESC LIMIT :limit OFFSET :offset";
$stmt = $pdo->prepare($sql);

foreach($params as $key => $value){
    $stmt->bindValue($key, $value);
}

$stmt->bindValue(':limit', $per_page, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();

$articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>News Aggregator</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </head>
    <body>
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1 class="my-5 fw-bold text-center">News Aggregator</h1>
                    <form action="index.php" method="GET">
                        <div class="d-flex gap-3 mb-4">
                            <input type="text" name="search" class="form-control" placeholder="Search By Keyword" value="<?= e($search) ?>">
                            <select name="category" class="form-select">
                                <option value="">All Categories</option>
                                <?php foreach($categories as $cat): ?>
                                    <option value="<?= e($cat) ?>" <?= $cat === $category ? 'selected' : '' ?>><?= e(ucfirst($cat)) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="btn btn-primary">Filter</button>
                        </div>
                    </form>

                    <?php if(empty($articles)): ?>
                        <p class="text-center text-muted">-- No Articles Found --</p>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach($articles as $article): ?>
                                <div class="col-md-4">
                                    <div class="card mb-4">
                                        <?php if($article['image_url']): ?>
                                            <img src="<?= e($article['image_url']) ?>" class="card-img-top" alt="<?= e($article['title']) ?>" loading="lazy">
                                        <?php endif; ?>
                                        <div class="card-body">
                                            <h5 class="card-title"><a href="view.php?id=<?= e((int)$article['id']) ?>" target="_blank"><?= e($article['title']) ?></a></h5>
                                            <p class="card-text"><small class="text-muted">Published Date: <?= e(date('F j, Y', strtotime($article['published_date'])) ) ?></small></p>
                                            <p class="card-text"><?= e(substr($article['description'] ?? '', 0, 100)) ?>...</p>
                                            <a href="<?= e($article['url']) ?>" target="_blank" class="btn btn-primary">Read More</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <?php for($i = 1; $i <= $pages; $i++): ?>
                                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&page=<?= $i ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </body>
</html>

